<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Static HTML Demo</title>
    <style>
      #wrapper {
      }
    </style>
  </head>
  <body>
    <div
      id="wrapper"
      data-tf-live="01K27Q48W64YXFYF2BH7KVJTBM"
      data-tf-on-ready="onTypeformReady"
      data-tf-on-started="onTypeformStarted"
      data-tf-on-submit="onTypeformSubmit"
      data-tf-on-question-changed="onTypeformQuestionChanged"
      data-tf-on-height-changed="onTypeformHeightChanged"
      data-tf-on-close="onTypeformClose"
      data-tf-on-ending-button-click="onTypeformEndingButtonClick"
    ></div>
    <script src="//embed.typeform.com/next/embed.js"></script>
    <script>
      // customers are often defining callbacks AFTER embedding the form
      // https://github.com/Typeform/embed/issues/363

      function onTypeformReady(data) {
        console.log('form ready', data)
      }

      function onTypeformStarted(data) {
        console.log('form started', data)
      }

      function onTypeformSubmit(data) {
        console.log('form submitted', data)
        if (data.response_id) {
    			window.location.href = '/loading?response_id=' + data.response_id + '&next_screen=/ticket'
  			} else {
    			console.error('Response ID not found')
    			window.location.href = '/loading?next_screen=/ticket' // Fallback URL
  			}
      }

      function onTypeformQuestionChanged(data) {
        console.log('form question changed', data)
      }

      function onTypeformHeightChanged(data) {
        console.log('form height changed', data)
      }

      function onTypeformClose(data) {
        console.log('modal window closed', data)
      }

      function onTypeformEndingButtonClick(data) {
        console.log('button on ending screen clicked', data)
      }
    </script>
  </body>
</html>
